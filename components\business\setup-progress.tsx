"use client"

import { CheckCircle, AlertCircle, Clock, MapPin, DollarSign, Store, Image, Users } from "lucide-react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import Link from "next/link"

interface SetupProgressProps {
  business?: {
    id: number
    name: string
    description?: string
    address?: string
    postcode?: string
    phone?: string
    email?: string
    logo_url?: string
    opening_hours?: any
    delivery_radius?: number
    minimum_order_amount?: number
    delivery_fee?: number
    is_approved?: boolean | null
  }
  className?: string
  compact?: boolean
}

interface SetupStep {
  id: string
  title: string
  description: string
  completed: boolean
  required: boolean
  icon: any
  tabId: string
  weight: number // For progress calculation
}

export default function SetupProgress({ business, className = "", compact = false }: SetupProgressProps) {
  const setupSteps: SetupStep[] = [
    {
      id: 'basic_info',
      title: 'Business Information',
      description: 'Name, description, and contact details',
      completed: !!(business?.name && business?.description && business?.phone),
      required: true,
      icon: Store,
      tabId: 'basic',
      weight: 25
    },
    {
      id: 'location',
      title: 'Location & Delivery',
      description: 'Address, postcode, and delivery settings',
      completed: !!(business?.address && business?.postcode && business?.delivery_radius),
      required: true,
      icon: MapPin,
      tabId: 'location',
      weight: 25
    },
    {
      id: 'hours',
      title: 'Opening Hours',
      description: 'When customers can place orders',
      completed: !!(business?.opening_hours && Object.keys(business.opening_hours).length > 0),
      required: true,
      icon: Clock,
      tabId: 'hours',
      weight: 25
    },
    {
      id: 'pricing',
      title: 'Pricing & Fees',
      description: 'Minimum order amount and delivery fees',
      completed: !!(business?.minimum_order_amount && business?.delivery_fee),
      required: true,
      icon: DollarSign,
      tabId: 'pricing',
      weight: 25
    },
    {
      id: 'branding',
      title: 'Logo & Branding',
      description: 'Upload your business logo',
      completed: !!(business?.logo_url),
      required: false,
      icon: Image,
      tabId: 'branding',
      weight: 10
    }
  ]

  const requiredSteps = setupSteps.filter(step => step.required)
  const completedRequired = requiredSteps.filter(step => step.completed)
  const totalRequiredWeight = requiredSteps.reduce((sum, step) => sum + step.weight, 0)
  const completedRequiredWeight = completedRequired.reduce((sum, step) => sum + step.weight, 0)
  const completionPercentage = totalRequiredWeight > 0 ? (completedRequiredWeight / totalRequiredWeight) * 100 : 0

  const allOptionalSteps = setupSteps.filter(step => !step.required)
  const completedOptional = allOptionalSteps.filter(step => step.completed).length

  const nextIncompleteStep = setupSteps.find(step => step.required && !step.completed)

  if (compact) {
    return (
      <Card className={`border-gray-200 ${className}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center gap-2">
              <div className={`p-1.5 rounded-full ${
                completionPercentage === 100 ? 'bg-green-100' : 'bg-blue-100'
              }`}>
                {completionPercentage === 100 ? (
                  <CheckCircle className="h-4 w-4 text-green-600" />
                ) : (
                  <AlertCircle className="h-4 w-4 text-blue-600" />
                )}
              </div>
              <div>
                <h3 className="font-medium text-sm">Setup Progress</h3>
                <p className="text-xs text-gray-600">
                  {completedRequired.length}/{requiredSteps.length} required steps
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-lg font-bold text-gray-900">
                {Math.round(completionPercentage)}%
              </div>
              <div className="text-xs text-gray-500">Done</div>
            </div>
          </div>

          <Progress value={completionPercentage} className="h-2 mb-3" />

          {nextIncompleteStep && (
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <nextIncompleteStep.icon className="h-4 w-4 text-orange-600" />
                <span className="text-sm text-gray-700">Next: {nextIncompleteStep.title}</span>
              </div>
              <Link href={`/business-admin/settings?tab=${nextIncompleteStep.tabId}`}>
                <Button size="sm" className="h-7 px-3 text-xs">
                  Set Up
                </Button>
              </Link>
            </div>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={`border-gray-200 ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">Business Setup Progress</CardTitle>
            <CardDescription>
              Complete these steps to optimize your business profile
            </CardDescription>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-900">
              {Math.round(completionPercentage)}%
            </div>
            <div className="text-sm text-gray-500">Done</div>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Progress Bar */}
        <div className="space-y-2">
          <Progress value={completionPercentage} className="h-3" />
          <div className="flex justify-between text-xs text-gray-600">
            <span>{completedRequired.length}/{requiredSteps.length} required steps</span>
            <span>
              {completedOptional.length > 0 && `+ ${completedOptional.length} optional`}
            </span>
          </div>
        </div>

        {/* Setup Steps */}
        <div className="space-y-3">
          {setupSteps.map((step) => (
            <div key={step.id} className={`flex items-center justify-between p-3 rounded-lg border ${
              step.completed ? 'bg-green-50 border-green-200' :
              step.required ? 'bg-orange-50 border-orange-200' :
              'bg-gray-50 border-gray-200'
            }`}>
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-full ${
                  step.completed ? 'bg-green-100 text-green-600' :
                  step.required ? 'bg-orange-100 text-orange-600' :
                  'bg-gray-100 text-gray-500'
                }`}>
                  {step.completed ? (
                    <CheckCircle className="h-5 w-5" />
                  ) : (
                    <step.icon className="h-5 w-5" />
                  )}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className={`font-medium ${
                      step.completed ? 'text-green-800' : 'text-gray-900'
                    }`}>
                      {step.title}
                    </span>
                    {step.required && !step.completed && (
                      <Badge variant="outline" className="bg-orange-100 text-orange-700 border-orange-300 text-xs">
                        Required
                      </Badge>
                    )}
                    {step.completed && (
                      <Badge variant="outline" className="bg-green-100 text-green-700 border-green-300 text-xs">
                        ✓ Done
                      </Badge>
                    )}
                  </div>
                  <div className="text-sm text-gray-600">{step.description}</div>
                </div>
              </div>

              {!step.completed && (
                <Link href={`/business-admin/settings?tab=${step.tabId}`}>
                  <Button
                    size="sm"
                    variant={step.required ? "default" : "outline"}
                    className={step.required ? "bg-orange-600 hover:bg-orange-700" : ""}
                  >
                    {step.required ? 'Set Up' : 'Add'}
                  </Button>
                </Link>
              )}
            </div>
          ))}
        </div>

        {/* Completion Message */}
        {completionPercentage === 100 ? (
          <div className="text-center p-4 bg-green-50 rounded-lg border border-green-200">
            <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <h3 className="font-medium text-green-900 mb-1">Setup Done! 🎉</h3>
            <p className="text-sm text-green-700 mb-3">
              Your business is ready to start receiving orders.
            </p>
            <Link href="/business-admin/orders">
              <Button className="bg-green-600 hover:bg-green-700">
                View Orders
              </Button>
            </Link>
          </div>
        ) : (
          <div className="text-center p-4 bg-blue-50 rounded-lg border border-blue-200">
            <AlertCircle className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <h3 className="font-medium text-blue-900 mb-1">Almost There!</h3>
            <p className="text-sm text-blue-700 mb-3">
              Complete the remaining {requiredSteps.length - completedRequired.length} required steps to finish your setup.
            </p>
            <Link href="/business-admin/settings">
              <Button className="bg-blue-600 hover:bg-blue-700">
                Complete Settings
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
